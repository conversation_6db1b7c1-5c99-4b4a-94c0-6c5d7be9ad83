import { After<PERSON>iewInit, Component, EventEmitter, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { AnimationOptions } from 'ngx-lottie';
import { takeUntil } from 'rxjs';

import { FolderNamesS3 } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getAWSImagePath } from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchProjectGalleryById,
  UpdateProjectGallery,
  fetchImageDropDown
} from 'src/app/reducers/project/project.action';
import {
  getProjectGalleryById,
  getProjectGalleryDropDown
} from 'src/app/reducers/project/project.reducer';
import { AddWaterMark } from 'src/app/reducers/property/property.actions';
import { getWaterMarkImage } from 'src/app/reducers/property/property.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'gallery',
  templateUrl: './gallery.component.html',
})
export class GalleryComponent implements OnInit, OnDestroy, AfterViewInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  galleryImageArray: any;
  galleryVideoArray: any;
  galleryDocArray: any;
  galleryS3Paths: Array<string> = [];
  galleryS3PathsDoc: Array<any> = [];
  galleryS3PathsVid: Array<any> = [];
  coverImg: string = '';
  coverImgIndex = 0;
  galleryMapping: any = {};
  galleryDropdownData: string[];
  addImage: AnimationOptions = {
    path: 'assets/animations/gallery-image.json',
  };
  addVideo: AnimationOptions = {
    path: 'assets/animations/gallery-video.json',
  };
  addDoc: AnimationOptions = {
    path: 'assets/animations/gallery-doc.json',
  };
  allImagePath: any;
  allVideoPath: any;
  allImagePathArr: any = [];
  allDocPathArr: any = [];
  s3BucketUrl = environment.s3ImageBucketURL;
  vidPathUrl: any;
  allVidPathArr: any = [];
  projectId: any;
  selectedDataId: any;
  galleryPayload: any = {};
  newUrl: string = '';
  links: any[] = [];
  isImageUploading: boolean = false;
  isVideoUploading: boolean = false;
  isDocumentUploading: boolean = false;


  waterMarkSettingsObj: any = {
    isWaterMarkEnabled: false,
    isToAddWaterMark: false,
    getFileFromFileUpload: false,
    watermarkLogo: '',
    watermarkPosition: '',
    watermarkOpacity: null,
    watermarkSize: null,
  };

  constructor(
    public router: Router,
    private activatedRoute: ActivatedRoute,
    private _notificationService: NotificationsService,
    private sharedDataService: ShareDataService,
    private imgService: BlobStorageService,
    private _store: Store<AppState>,
    public trackingService: TrackingService
  ) { }

  ngOnInit(): void {
    this.sharedDataService.updateSharedTabData(4);
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.waterMarkSettingsObj.isWaterMarkEnabled =
          data?.leadProjectSetting?.isWaterMarksOnImagesEnabled;
        this.waterMarkSettingsObj.watermarkLogo = data
          ? data?.leadProjectSetting?.waterMarkUrl
          : null;
        this.waterMarkSettingsObj.watermarkOpacity =
          data?.leadProjectSetting?.opacity;
        this.waterMarkSettingsObj.watermarkPosition =
          data?.leadProjectSetting?.waterMarkPosition;
        this.waterMarkSettingsObj.watermarkSize =
          data?.leadProjectSetting?.imageSize;
      });

    this._store
      .select(getWaterMarkImage)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (!this.waterMarkSettingsObj.getFileFromFileUpload) {
          return;
        }
        let waterMarkUrl = data ? data : [];
        waterMarkUrl?.forEach((data: any) => {
          let s3bucket = this.s3BucketUrl;
          let startIndex = data.indexOf(s3bucket);
          if (startIndex !== -1) {
            let slicedString = data.slice(startIndex + s3bucket.length);
            this.galleryS3Paths.push(slicedString);
          }
        });
        this.allImagePathArr = [];
        this.galleryS3Paths?.map((item: any) => {
          this.allImagePathArr.push({
            imageFilePath: item,
            isCoverImage: this.coverImg === item ? true : false,
            galleryType: 1,
          });
          this.galleryMapping = {
            ...this.galleryMapping,
            [item]: this.galleryMapping[item] || this.galleryDropdownData[0],
          };
        });
      });

    this._store.select(getProjectGalleryById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data) => {
        this.waterMarkSettingsObj.isToAddWaterMark = data?.[0]?.isWaterMarkEnabled;
        let brochures = data?.[0]?.brochures;
        // this.galleryS3PathsDoc = [];
        // this.allDocPathArr = [];
        this.coverImg = null;
        if (Array.isArray(data?.[0]?.links)) {
          this.links = data?.[0].links.filter((link: null) => link !== null);
        } else {
          this.links = [];
        }

        this.galleryS3PathsDoc = [];
        this.allDocPathArr = [];
        brochures?.map((item: any) => {
          this.galleryS3PathsDoc.push({
            name: item?.name,
            path: item?.url,
          });
          this.allDocPathArr.push({
            name: item?.name,
            url: item?.url,
          });
        });

        let galleryImages = data?.[0]?.images;
        let images = galleryImages ? Object.entries(galleryImages) : [];

        this.galleryS3Paths = [];
        this.galleryMapping = {};
        if (images.length > 0) {
          images?.map((arr: any) => {
            let arrImg = arr[1];
            arrImg.map((img: any) => {
              this.galleryMapping[img.imageFilePath] = arr[0];
              if (!this.galleryS3Paths?.includes(img.imageFilePath)) {
                this.galleryS3Paths?.push(img.imageFilePath);
                if (img.isCoverImage) {
                  this.coverImgIndex = this.galleryS3Paths.length - 1;
                  this.coverImg = img.imageFilePath;
                }
              }
            });
          });
        }
        if (!this.coverImg?.length && this.galleryS3Paths.length > 0) {
          this.coverImg = this.galleryS3Paths[0];
          this.coverImgIndex = 0;
        } else if (this.galleryS3Paths.length === 0) {
          this.coverImg = '';
          this.coverImgIndex = 0;
        }

        let galleryVideos = data?.[0]?.videos;
        this.galleryS3PathsVid = [];
        this.allVidPathArr = [];
        galleryVideos?.map((item: any) => {
          if (!this.galleryS3PathsVid.includes(item.imageFilePath)) {
            this.galleryS3PathsVid?.push({
              name: item?.name,
              imageFilePath: item.imageFilePath,
            });
            this.allVidPathArr.push({
              name: item?.name,
              imageFilePath: item.imageFilePath,
              isCoverImage: item.isCoverImage,
              galleryType: 2,
            });
          }
        });
      });

    this._store.dispatch(new fetchImageDropDown());
    this._store
      .select(getProjectGalleryDropDown)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.galleryDropdownData = data?.filter(
          (item: any) => item !== 'videos'
        );
        this.galleryDropdownData?.unshift('Select field');
      });
  }

  ngAfterViewInit(): void {
    this.activatedRoute.params
      .pipe(takeUntil(this.stopper))
      .subscribe((params: any) => {
        if ((params || {}).id) {
          this.selectedDataId = params.id;
          this.sharedDataService.setProjectTitleId(params.id);
          this.galleryS3Paths = [];
          this.galleryS3PathsDoc = [];
          this.galleryS3PathsVid = [];
          this.coverImg = '';
          this.coverImgIndex = 0;
          this.galleryMapping = {};
          this.allImagePathArr = [];
          this.allDocPathArr = [];
          this.allVidPathArr = [];
          this.links = [];
          this._store.dispatch(new FetchProjectGalleryById(this.selectedDataId));
        }
      });
  }

  uploadImage(e: any) {
    if (this.waterMarkSettingsObj.isToAddWaterMark) return;
    this.trackingService.trackFeature(`Web.ProjectGallery.Button.Photos.Click`)
    let fileSize = e;
    if (fileSize && Array.isArray(fileSize)) {
      if (fileSize.some((size) => size[2] > 500 * 1024 * 1024)) {
        this._notificationService.warn(`File size should be less than 500 MB.`);
        return;
      }
    }
    this.galleryImageArray = e;
    this.isImageUploading = true;
    if (this.galleryImageArray?.length) {
      let filesToBeUploadToS3Bucket = this.galleryImageArray
        .filter((imagePath: any) => {
          return imagePath[3] instanceof File;
        })
        .map((imagePath: any) => imagePath[3]);

      if (filesToBeUploadToS3Bucket.length) {
        this.imgService
          .uploadGalleryFilesMax500MB(filesToBeUploadToS3Bucket, FolderNamesS3.Images)
          .pipe(takeUntil(this.stopper))
          .subscribe((res: any) => {
            if (res?.data) {
              this.isImageUploading = false;

              let pathArr = res?.data;
              pathArr?.map((path: string) => {
                this.galleryS3Paths?.push(path);
              });
              this.allImagePathArr = [];
              this.galleryS3Paths?.map((item: any) => {
                this.allImagePathArr.push({
                  imageFilePath: item,
                  isCoverImage: this.coverImg === item ? true : false,
                  galleryType: 1,
                });
                this.galleryMapping = {
                  ...this.galleryMapping,
                  [item]:
                    this.galleryMapping[item] || this.galleryDropdownData[0],
                };
              });
              this.sharedDataService.setProjectTitleId(this.selectedDataId);
            }
          });
      }
    }
  }

  uploadFile(e: any) {
    let fileSize = e;
    this.isDocumentUploading = true
    if (fileSize && Array.isArray(fileSize)) {
      if (fileSize.some((size) => size[2] > 15728640)) {
        this._notificationService.warn(`File size should be less than 15 MB.`);
        return;
      }
    }
    this.trackingService.trackFeature(`Web.ProjectGallery.Button.Brochures.Click`)
    this.galleryDocArray = e;
    if (this.galleryDocArray?.length) {
      let imagesToBeUploadToS3Bucket = this.galleryDocArray
        .filter((imagePath: string) => {
          return imagePath[0].startsWith('data:');
        })
        .map((imagePath: string) => imagePath[0]);
      this.imgService
        .uploadPropertyGallery(imagesToBeUploadToS3Bucket)
        .pipe(takeUntil(this.stopper))
        .subscribe((res: any) => {
          if (res?.data) {
            this.isDocumentUploading = false
            let pathArr = res?.data;
            pathArr?.map((path: string, index: number) => {
              this.allVideoPath = getAWSImagePath(path);
              this.galleryS3PathsDoc.push({
                path: this.allVideoPath,
                name: this.galleryDocArray[index][1],
              });
            });
            this.allDocPathArr = [];
            this.galleryS3PathsDoc.map((item: any) => {
              this.allDocPathArr.push({
                name: item?.name,
                url: item.path,
              });
            });
          }
        });
    }
  }

  uploadVideo(e: FileList) {
    this.isVideoUploading = true
    let fileSize = e;
    if (fileSize && Array.isArray(fileSize)) {
      if (fileSize.some((size) => size[2] > 500 * 1024 * 1024)) {
        this._notificationService.warn(`Video size should be less than 500 MB.`);
        return;
      }
    }
    this.trackingService.trackFeature(`Web.ProjectGallery.Buttom.Videos.Click`)
    this.galleryVideoArray = e;
    if (this.galleryVideoArray?.length) {
      let filesToBeUploadToS3Bucket = this.galleryVideoArray
        .filter((imagePath: any) => {
          return imagePath[3] instanceof File;
        })
        .map((imagePath: any) => imagePath[3]);
      if (filesToBeUploadToS3Bucket.length) {
        this.imgService
          .uploadGalleryFilesMax500MB(filesToBeUploadToS3Bucket, FolderNamesS3.Images)
          .pipe(takeUntil(this.stopper))
          .subscribe((res: any) => {
            if (res?.data) {
              this.isVideoUploading = false
              let pathArr = res?.data;
              pathArr?.map((path: string, index: number) => {
                this.vidPathUrl = getAWSImagePath(path);
                this.galleryS3PathsVid?.push({
                  imageFilePath: this.vidPathUrl,
                  name: this.galleryVideoArray[index][1],
                });
                this.allVidPathArr.push({
                  name: this.galleryVideoArray[index][1],
                  imageFilePath: this.vidPathUrl,
                  isCoverImage: true,
                  galleryType: 2,
                });
              });
            }
          });
      }
    }
  }

  deleteImage(index: number, url: any) {
    if (url === this.coverImg) {
      this.coverImgIndex = 0;
    }
    this.galleryS3Paths.splice(index, 1);
    this.trackingService.trackFeature(`Web.ProjectGallery.Photos.Delete.Click`)
  }

  deleteDoc(index: any) {
    this.galleryS3PathsDoc.splice(index, 1);
    this.allDocPathArr = this.allDocPathArr.filter(
      (item: any, idx: any) => idx !== index
    );
    this.trackingService.trackFeature(`Web.ProjectGallery.Brochures.Delete.Click`)
  }

  deleteVideo(index: any) {
    this.galleryS3PathsVid.splice(index, 1);
    this.allVidPathArr = this.allVidPathArr.filter(
      (item: any, idx: any) => idx !== index
    );
    this.trackingService.trackFeature(`Web.ProjectGallery.Videos.Delete.Click`)
  }

  getVidPath(imagePath: string) {
    return getAWSImagePath(imagePath);
  }

  onSetCoverImage(img: string, index: number) {
    this.trackingService.trackFeature(`Web.ProjectGallery.Button.Cover.Click`)
    this.coverImgIndex = index;
    this.coverImg = img;

    this.allImagePathArr?.forEach((item: any) => {
      item.isCoverImage = false;
    });

    // Remove previous cover image entry, if any
    this.allImagePathArr = this.allImagePathArr?.filter(
      (item: any) => item.imageFilePath !== this.coverImg
    );

    // Add new cover image entry
    this.allImagePathArr?.push({
      imageFilePath: this.coverImg,
      isCoverImage: true,
      galleryType: 1,
    });
  }

  addInputField() {
    if (typeof this.newUrl === 'string' && this.newUrl.trim() !== '') {
      this.links = [...this.links, this.newUrl.trim()];
      this.newUrl = '';
      this.trackingService.trackFeature(`Web.ProjectGallery.Button.AddProjectURL.Click`)
    }
  }

  removeInputField(link: string) {
    const index = this.links.indexOf(link);
    if (index !== -1) {
      this.links.splice(index, 1);
    }
  }

  saveAndFinish() {
    let gallery: any = [];
    Object.entries(this.galleryMapping).forEach(([imageFilePath, name]) => {
      if (this.galleryS3Paths.includes(imageFilePath)) {
        const isCoverImage =
          this.galleryS3Paths[this.coverImgIndex] === imageFilePath;
        gallery.push({
          name: name,
          imageFilePath: imageFilePath,
          imageKey: imageFilePath,
          isCoverImage: isCoverImage,
          galleryType: 1,
        });
      }
    });

    this.allVidPathArr.forEach((videoFile: any) => {
      gallery.push({
        name: videoFile.name || "Video",
        imageFilePath: videoFile.imageFilePath || "",
        imageKey: videoFile.imageFilePath || "",
        isCoverImage: videoFile.isCoverImage || false,
        galleryType: 2,
      });
    });

    const payload = {
      projectId: this.selectedDataId,
      gallery: gallery,
      isWaterMarkEnabled: this.waterMarkSettingsObj.isToAddWaterMark,
      brochureDtos: this.allDocPathArr.map((doc: any) => ({
        name: doc.name,
        url: doc.url,
      })),
      links: this.links,
    };
    this.sharedDataService.setProjectTitleId(this.selectedDataId);
    this._store.dispatch(new UpdateProjectGallery(payload));
    this.router.navigate(['/projects/manage-projects']);
  }

  setImageCategory(e: any, url: any) {
    this.galleryMapping = {
      ...this.galleryMapping,
      [url]: e,
    };
    this.trackingService.trackFeature(`Web.ProjectGallery.Photos.SelectField.Click`)
  }

  waterMarkImages(data: any) {
    if (!this.waterMarkSettingsObj.isToAddWaterMark) return;
    let filedata = data;
    if (filedata && Array.isArray(filedata)) {
      if (filedata.some((size) => size.size > 5000000)) {
        return;
      }
    }
    let imgURL = this.waterMarkSettingsObj.watermarkLogo;
    let wtaerMarkPayload = {
      Url: imgURL,
      files: filedata,
      WaterMarkPosition:
        this.waterMarkSettingsObj.watermarkPosition === 0
          ? '0'
          : this.waterMarkSettingsObj.watermarkPosition || 0,
      Opacity: this.waterMarkSettingsObj.watermarkOpacity || 100,
      ImageSize: this.waterMarkSettingsObj.watermarkSize || 7,
      Background: false,
    };
    this.waterMarkSettingsObj.getFileFromFileUpload = true;
    this._store.dispatch(new AddWaterMark(wtaerMarkPayload));
  }

  ngOnDestroy() {
    this.waterMarkSettingsObj.getFileFromFileUpload = false;
    this.stopper.emit();
    this.stopper.complete();
  }
}