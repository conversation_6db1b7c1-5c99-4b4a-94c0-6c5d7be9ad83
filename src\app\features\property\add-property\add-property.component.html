<div (click)="goToManage()" [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
  class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<ng-container *ngIf="!showSubmitConfirmation">
  <div class="top-navbar border-top position-fixed w-100 z-index-2"
    [ngClass]="{'pe-none blinking': isSelectedPropertyInfoLoading}">
    <ul>
      <li (click)="handleStepsNavigation(1)"><a
          [ngClass]="currentStep==1 ? 'text-black-100 py-12 border-bottom-black' : 'text-black-200 fw-300'">
          {{ 'USER_MANAGEMENT.basic-info' | translate }}</a></li>
      <li (click)="handleStepsNavigation(2)"><a
          [ngClass]="currentStep==2 ? 'text-black-100 py-12 border-bottom-black' : 'text-black-200 fw-300'">
          {{ 'PROPERTY.STEPS.property-info' | translate }}</a></li>
      <!-- <ng-container *ngIf="stepOneSubmitted || activePropertyId"> -->
      <li (click)="handleStepsNavigation(3)">
        <a
          [ngClass]="currentStep==3 ? 'text-black-100 py-12 border-bottom-black' : 'text-black-200 fw-300'">Facilities</a>
      </li>
      <li (click)="handleStepsNavigation(4)"><a
          [ngClass]="currentStep==4 ? 'text-black-100 py-12 border-bottom-black' : 'text-black-200 fw-300'">
          {{ 'PROPERTY.STEPS.gallery' | translate }}</a></li>
      <!-- </ng-container> -->
    </ul>
  </div>
  <div class="mt-40 position-relative d-flex w-100 tb-flex-col"
    [ngClass]="{'pe-none blinking': isSelectedPropertyInfoLoading}">
    <div class="flew-grow-1 w-100">
      <div *ngIf="currentStep==1">
        <div class="w-100">
          <div
            class="position-fixed right-20 top-55 align-center z-index-2 ip-top-100 ip-w-100 ip-bg-white ip-p-8 ip-right-0">
            <u class="ml-20 fw-semi-bold text-mud cursor-pointer" (click)="goToManage()">{{
              'BUTTONS.cancel' | translate }}</u>
            <button class="btn-coal btn-xs ml-20" (click)="handleNextClick()">{{ 'BUTTONS.next' | translate }}</button>
          </div>
        </div>
        <form [formGroup]="basicInfoForm" autocomplete="off" class="px-30 pt-12 pb-30 scrollbar h-100-90 scroll-hide">
          <div class="d-flex w-100 ip-flex-col">
            <div class="w-60pr ip-w-100">
              <div class="img-radio">
                <div class="field-label-req">{{ 'PROPERTY.BASIC_INFO.want-to' | translate }}</div>
                <div class="d-flex">
                  <form-errors-wrapper [control]="basicInfoForm.controls['enquiredFor']"
                    label="{{'LEAD_FORM.enquired-for' | translate }}">
                    <ng-container *ngFor="let enquiry of enquiredForList">
                      <input type="radio" class="btn-check" name="enquiredFor" [id]="enquiry.type" autocomplete="off"
                        formControlName="enquiredFor" [value]="enquiry.type">
                      <label class="btn btn-outline-secondary mr-10 position-relative" [for]="enquiry.type">
                        <img [type]="'leadrat'" [appImage]="enquiry.img" alt="Transaction Type" width="130" height="95"
                          class="ph-w-70px ph-h-unset">
                        <span class="text-label">{{enquiry.type == 'Rent' ? 'Rent / Lease'
                          :enquiry.type}}</span></label>
                    </ng-container>
                  </form-errors-wrapper>
                </div>
              </div>
              <div class="img-radio">
                <div class="field-label-req">{{'LABEL.property' | translate }} {{'LABEL.type' | translate }}</div>
                <div class="d-flex">
                  <form-errors-wrapper [control]="basicInfoForm.controls['propertyType']"
                    label="{{'LABEL.property' | translate }} {{'LABEL.type' | translate}}">
                    <ng-container *ngFor="let pType of propertyTypeList">
                      <input type="radio" class="btn-check" name="propertyType" [id]="pType.type" autocomplete="off"
                        [value]="pType.displayName" formControlName="propertyType">
                      <label class="btn btn-outline-secondary mr-10 mb-10 position-relative" [ngClass]="pType.type"
                        [for]="pType?.type">
                        <img [type]="'leadrat'" [appImage]="propertyTypeImages[pType.type]" [alt]="pType.displayName"
                          width="125" heigh="90" class="ph-w-70px ph-h-unset">
                        <span class="text-label">{{pType.displayName}}</span></label>
                    </ng-container>
                  </form-errors-wrapper>
                </div>
              </div>
              <div class="box-radio" *ngIf="basicInfoForm.controls['propertyType'].value">
                <div class="field-label-req">{{'PROPERTY.sub-type' | translate}}</div>
                <form-errors-wrapper [control]="basicInfoForm.controls['propertySubType']"
                  label="{{'PROPERTY.sub-type' | translate}}">
                  <div class="d-flex flex-wrap">
                    <ng-container *ngFor="let pSubType of propertySubTypeList">
                      <input type="radio" class="btn-check" name="propertySubType" [id]="pSubType.displayName"
                        autocomplete="off" formControlName="propertySubType" [value]="pSubType.displayName">
                      <label class="btn-outline" [for]="pSubType.displayName">{{pSubType.displayName}}</label>
                    </ng-container>
                  </div>
                </form-errors-wrapper>
              </div>
              <ng-container *ngIf="basicInfoForm.controls['propertySubType'].value != 'Plot' && isResidential">
                <div class="box-radio">
                  <div class="field-label-req">{{currentPath.includes('listing') ? "BR" : "BHK"}}</div>
                  <form-errors-wrapper [control]="basicInfoForm.controls['noOfBHK']"
                    [label]="currentPath.includes('listing')  ? 'BR' : 'BHK'">
                    <div class="d-flex flex-wrap">
                      <ng-container *ngFor="let bhkNo of bhkNoList">
                        <input type="radio" class="btn-check" name="noOfBHK" [id]="bhkNo" autocomplete="off"
                          formControlName="noOfBHK" (click)="appendToBHKList(bhkNo)" [value]="bhkNo">
                        <label class="btn-outline" [for]="bhkNo">{{currentPath.includes('listing') ?
                          getBRDisplayString(bhkNo) :getBHKDisplayString(bhkNo)}}</label>
                      </ng-container>
                    </div>
                  </form-errors-wrapper>
                </div>
                <div *ngIf="!currentPath.includes('listing')" class="box-radio">
                  <div class="field-label-req">
                    {{'PROPERTY.bhk' | translate }} {{'LABEL.type' |
                    translate}}</div>
                  <div class="d-flex">
                    <form-errors-wrapper [control]="basicInfoForm.controls['bhkType']"
                      label="{{'PROPERTY.bhk' | translate }} {{'LABEL.type' | translate}}">
                      <ng-container *ngFor="let bhkType of bhkTypeList">
                        <input type="radio" class="btn-check" [id]="bhkType + '-bhkType'" autocomplete="off"
                          [value]="bhkType" formControlName="bhkType">
                        <label class="btn-outline" [for]="bhkType + '-bhkType'">{{bhkType}}</label>
                      </ng-container>
                    </form-errors-wrapper>
                  </div>
                </div>
              </ng-container>

            </div>
            <div class="border-right mx-20 mt-20 mb-40 flex-grow-1 ip-d-none"></div>
            <div class="w-40pr ip-w-100">
              <div>
                <div class="flex-between">
                  <label class="field-label-req">{{'PROPERTY.PROPERTY_DETAIL.title' | translate}}
                  </label>
                  <div class="align-center mt-16">
                    <label class="checkbox-container fw-semi-bold">
                      <input type="checkbox" id="inpHighlight" data-automate-id="inpHighlight"
                        formControlName="isFeatured">
                      <span class="checkmark"></span><span class="header-6">{{ 'PROPERTY.highlight-property' | translate
                        }}</span>
                    </label>
                  </div>
                </div>
                <form-errors-wrapper [control]="basicInfoForm.controls['title']"
                  label="{{'PROPERTY.PROPERTY_DETAIL.title' | translate}}">
                  <input type="text" id="inpPropTitle" data-automate-id="inpPropTitle" placeholder="Enter Title"
                    formControlName="title">
                </form-errors-wrapper>
              </div>
              <div *ngIf="isClickCoWorkingOfficeSpace">
                <div class="field-label">Co Working Operator</div>
                <form-errors-wrapper>
                  <input type="text" id="inpPropTitle" data-automate-id="coWorkingOperator" placeholder="Enter Operator"
                    formControlName="coWorkingOperator">
                </form-errors-wrapper>
              </div>
              <div *ngIf="canAssign && activeUsers?.length">
                <div class='field-label'>Assign To</div>
                <ng-select [virtualScroll]="true" placeholder="Select User" [items]="userList"
                  [ngClass]="{'disabled' : !canAssign}" [multiple]="true" [closeOnSelect]="false" bindLabel="fullName"
                  bindValue="id" name="assignedTo" formControlName="assignedTo" class="bg-white"
                  (change)="onSelectionChange($event, 'assignedTo')">
                  <ng-template ng-label-tmp let-item="item" let-clear="clear">
                    <div class="flex-between">
                      <div class="align-center m-4">
                        <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                        <span class="ng-value-label text-truncate-1 break-all">{{item.firstName + ' ' +
                          item.lastName}}</span>
                      </div>
                      <span class="error-text" *ngIf="!item.isActive">( Disabled
                        )</span>
                    </div>
                  </ng-template>
                  <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container">
                      <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                        [checked]="item$.selected" [disabled]="!item.isActive">
                      <span class="checkmark"></span>
                      <div class="flex-between">
                        <span class="text-truncate-1 break-all">{{item.firstName}} {{item.lastName}}</span>
                        <span class="error-text" *ngIf="!item.isActive">( Disabled )</span>
                      </div>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
              <div *ngIf="basicInfoForm.controls['propertySubType'].value == 'Plot'" class="align-end w-100">
                <div class="w-33">
                  <div class="field-label">{{'PROPERTY.PROPERTY_DETAIL.length' | translate}}</div>
                  <div class="mr-10">
                    <form-errors-wrapper [control]="basicInfoForm.controls['propertyLength']"
                      label="{{'PROPERTY.PROPERTY_DETAIL.length' | translate}}">
                      <input type="number" min="0" id="inpPropLength" data-automate-id="inpPropLength"
                        placeholder="Length" formControlName="propertyLength" (input)="lengthAndBreadthChanged()">
                    </form-errors-wrapper>
                  </div>
                </div>
                <div class="w-33">
                  <div class="field-label">{{'PROPERTY.PROPERTY_DETAIL.breadth' | translate}}</div>
                  <div class="mr-10">
                    <form-errors-wrapper [control]="basicInfoForm.controls['propertyBreadth']"
                      label="{{'PROPERTY.PROPERTY_DETAIL.breadth' | translate}}">
                      <input type="number" min="0" id="inpPropBreadth" data-automate-id="inpPropBreadth"
                        placeholder="Breadth" formControlName="propertyBreadth" (input)="lengthAndBreadthChanged()">
                    </form-errors-wrapper>
                  </div>
                </div>
                <div class="w-33 mt-40">
                  <form-errors-wrapper [control]="basicInfoForm.controls['lengthAndBreadthAreaUnit']"
                    label="{{'PROJECTS.size-unit' | translate}}">
                    <ng-select [virtualScroll]="true" tabindex="4" placeholder="Select Unit"
                      [ngClass]="{'pe-none blinking': isAreaSizeUnitsLoading}"
                      formControlName="lengthAndBreadthAreaUnit"
                      [readonly]="!(basicInfoForm.controls['propertyLength'].value != null && basicInfoForm.controls['propertyBreadth'].value != null)"
                      [items]="areaSizeUnits" bindValue="id" bindLabel="unit"></ng-select>
                  </form-errors-wrapper>
                </div>
              </div>
              <div>
                <div class="field-label-req">Property Area</div>
                <div class="w-100 align-center">
                  <div class="w-50">
                    <form-errors-wrapper [control]="basicInfoForm.controls['propertySize']" label="Property Area">
                      <input type="number" min="0" required id="inpPropSize" data-automate-id="inpPropSize"
                        placeholder="Enter Property Area" formControlName="propertySize"
                        [readonly]="!readonlyCondition">
                    </form-errors-wrapper>
                  </div>
                  <div class="w-50 pl-8">
                    <form-errors-wrapper [control]="basicInfoForm.controls['areaUnit']"
                      label="{{'PROJECTS.size-unit' | translate}}">
                      <ng-select [virtualScroll]="true" tabindex="4" placeholder="Select Area Unit"
                        [ngClass]="{'pe-none blinking': isAreaSizeUnitsLoading}" formControlName="areaUnit" required
                        [readonly]="!basicInfoForm.controls['propertySize'].value || !readonlyCondition"
                        [items]="areaSizeUnits" bindValue="id" bindLabel="unit"
                        (change)="onUnitChange('areaUnit')"></ng-select>
                    </form-errors-wrapper>
                  </div>
                </div>
              </div>
              <div *ngIf="(!isAgricultural || isFarmHouse) && !isPlot">
                <div>
                  <div [ngClass]="basicInfoForm.controls['carpetArea'].value ? 'field-label-req' : 'field-label'">
                    {{'LEADS.carpet-area'
                    | translate}}</div>
                  <div class="w-100 align-center">
                    <div class="w-50">
                      <form-errors-wrapper [control]="basicInfoForm.controls['carpetArea']"
                        label="{{'LEADS.carpet-area' | translate}}">
                        <input type="number" min="0" id="inpCarpetArea" data-automate-id="inpCarpetArea"
                          placeholder="Enter Carpet Area" formControlName="carpetArea">
                      </form-errors-wrapper>
                    </div>
                    <div class="w-50 pl-8">
                      <form-errors-wrapper [control]="basicInfoForm.controls['carpetAreaUnit']"
                        label="{{'PROJECTS.size-unit' | translate}}">
                        <ng-select [virtualScroll]="true" tabindex="4" placeholder="Select Area Unit"
                          [ngClass]="{'pe-none blinking': isAreaSizeUnitsLoading}" formControlName="carpetAreaUnit"
                          [readonly]="!basicInfoForm.controls['carpetArea'].value" [items]="areaSizeUnits"
                          bindValue="id" bindLabel="unit" (change)="onUnitChange('carpetAreaUnit')"></ng-select>
                      </form-errors-wrapper>
                    </div>
                  </div>
                </div>
                <div>
                  <div [ngClass]="basicInfoForm.controls['buildUpArea'].value ? 'field-label-req' : 'field-label'">
                    {{'PROPERTY.PROPERTY_DETAIL.built-up-area'
                    | translate}}</div>
                  <div class="w-100 align-center">
                    <div class="w-50">
                      <form-errors-wrapper [control]="basicInfoForm.controls['buildUpArea']"
                        label="{{'PROPERTY.PROPERTY_DETAIL.built-up-area' | translate}}">
                        <input type="number" min="0" id="inpBuildUpArea" data-automate-id="inpBuildUpArea"
                          placeholder="Enter Built-up Area" formControlName="buildUpArea">
                      </form-errors-wrapper>
                    </div>
                    <div class="w-50 pl-8">
                      <form-errors-wrapper [control]="basicInfoForm.controls['buildUpAreaUnit']"
                        label="{{'PROJECTS.size-unit' | translate}}">
                        <ng-select [virtualScroll]="true" tabindex="4" placeholder="Select Area Unit"
                          [ngClass]="{'pe-none blinking': isAreaSizeUnitsLoading}" formControlName="buildUpAreaUnit"
                          [readonly]="!basicInfoForm.controls['buildUpArea'].value" [items]="areaSizeUnits"
                          bindValue="id" bindLabel="unit" (change)="onUnitChange('buildUpAreaUnit')"></ng-select>
                      </form-errors-wrapper>
                    </div>
                  </div>
                </div>
                <div>
                  <div [ngClass]="basicInfoForm.controls['saleableArea'].value ? 'field-label-req' : 'field-label'">
                    {{'PROPERTY.PROPERTY_DETAIL.saleable-area'
                    | translate}}</div>
                  <div class="w-100 align-center">
                    <div class="w-50">
                      <form-errors-wrapper [control]="basicInfoForm.controls['saleableArea']"
                        label="{{'PROPERTY.PROPERTY_DETAIL.saleable-area' | translate}}">
                        <input type="number" min="0" id="inpSaleableArea" data-automate-id="inpSaleableArea"
                          placeholder="Enter Saleable Area" formControlName="saleableArea">
                      </form-errors-wrapper>
                    </div>
                    <div class="w-50 pl-8">
                      <form-errors-wrapper [control]="basicInfoForm.controls['saleableAreaUnit']"
                        label="{{'PROJECTS.size-unit' | translate}}">
                        <ng-select [virtualScroll]="true" tabindex="4" placeholder="Select Area Unit"
                          [ngClass]="{'pe-none blinking': isAreaSizeUnitsLoading}" formControlName="saleableAreaUnit"
                          [readonly]="!basicInfoForm.controls['saleableArea'].value" [items]="areaSizeUnits"
                          bindValue="id" bindLabel="unit" (change)="onUnitChange('saleableAreaUnit')"></ng-select>
                      </form-errors-wrapper>
                    </div>
                  </div>
                </div>
              </div>
              <div class="form-group">
                <div class="flex-between">
                  <div class="field-label">{{'PROPERTY.about-property'| translate}}
                  </div>
                  <div *ngIf="basicInfoForm.controls['aboutProperty'].value" class="cursor-pointer mt-16"
                    (click)="openPreview('eng',false)">
                    <h6 class="flex-center text-green-250 fw-400"><span
                        class="ic-eye-solid ic-xxs ic-accent-green mr-4"></span>Preview
                    </h6>
                  </div>
                </div>
                <textarea rows="3" id="txtPropAbt" data-automate-id="txtPropAbt" placeholder="Enter About Property"
                  formControlName="aboutProperty" class="scrollbar"></textarea>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div *ngIf="currentStep==2">
        <div class="ip-h-60 w-100 bg-white">
          <div
            class="position-fixed right-20 top-55 align-center z-index-2 ip-top-100  ip-w-100 ip-bg-white ip-p-8 ip-right-0">
            <u class="ml-20 fw-semi-bold text-mud cursor-pointer" (click)="goToManage()">{{
              'BUTTONS.cancel' | translate }}</u>
            <button class="btn-gray btn-xs ml-20" (click)="handlePreviousClick()">{{ 'BUTTONS.back' | translate
              }}</button>
            <button *ngIf="!activePropertyId?.id && !activePropertyId" class="btn-coal btn-xs ml-20"
              (click)="onSaveAndNextClickStep2()">
              <span *ngIf="!isPropertyAddIsLoading else buttonDots">
                {{ 'BUTTONS.save' | translate }} & {{ 'BUTTONS.next' | translate }}
              </span>
            </button>
            <button *ngIf="activePropertyId?.id || activePropertyId" class="btn-coal btn-xs ml-20"
              (click)="handleNextClick()">
              {{ 'BUTTONS.next' | translate }}
            </button>
          </div>
        </div>
        <form [formGroup]="propertyDetailForm" autocomplete="off"
          class="px-30 pt-12 pb-30 scrollbar h-100-90 scroll-hide">
          <div class="d-flex w-100">
            <div class="d-flex w-100 ip-flex-col">
              <div class="w-50 ip-w-100 mt-20">
                <div class="img-radio">
                  <div>
                    <ng-container *ngFor="let saleType of saleTypeList">
                      <input type="radio" class="btn-check" name="saleType" [id]="saleType.displayName"
                        autocomplete="off" formControlName="saleType" [value]="saleType.displayName">
                      <label class="btn btn-outline-secondary mr-10 position-relative" [for]="saleType.displayName">
                        <img [type]="'leadrat'" [appImage]="saleType.img" [alt]="saleType.displayName" width="130"
                          heigh="95" class="ph-w-70px ph-h-unset">
                        <span class="text-label">{{saleType.displayName}}</span></label>
                    </ng-container>
                  </div>
                </div>

                <div>
                  <div class="mr-12">
                    <div class="field-label-req">Possession Needed By</div>
                    <form-errors-wrapper [control]="propertyDetailForm.controls['possessionDate']"
                      label="Possession Needed By">
                      <div class="form-group field-rupees-tag">
                        <ng-container *ngIf="basicInfoForm.controls['enquiredFor'].value === 'Rent'">
                          <span>
                            <div class="rupees icon ic-calendar ic-xxs ic-coal cursor-pointer">
                            </div>
                            <input type="text" id="inpPossessionDate" placeholder="Enter Possession Date" readonly
                              [formControl]="propertyDetailForm.get('possessionDate')" autocomplete="off"
                              [owlDateTimeTrigger]="dtGeneralPicker" [owlDateTime]="dtGeneralPicker"
                              (dateTimeChange)="onDateSelected($event)" />
                            <owl-date-time #dtGeneralPicker [pickerType]="'calendar'" [startAt]="currentDate"
                              (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                          </span>
                        </ng-container>
                        <ng-container *ngIf="basicInfoForm.controls['enquiredFor'].value === 'Sale'">
                          <span>
                            <div class="rupees icon ic-calendar ic-xxs ic-coal cursor-pointer"
                              (click)="isOpenPossessionModal = !isOpenPossessionModal">
                            </div>
                            <input type="text" id="inpPossessionDate" placeholder="Enter Possession Date" readonly
                              [value]="selectedPossession ? selectedPossession : (propertyDetailForm.value.globalRange || '')"
                              (click)="isOpenPossessionModal = !isOpenPossessionModal" autocomplete="off" [ngClass]="{
            'border-red-30': propertyDetailForm.controls['possessionDate'].invalid &&
            propertyDetailForm.controls['possessionDate'].touched
          }" />
                          </span>
                        </ng-container>
                      </div>
                    </form-errors-wrapper>
                    <div *ngIf="basicInfoForm.controls['enquiredFor'].value === 'Sale'" class="position-relative">
                      <div class="position-absolute top-0 w-100 bg-white z-index-1001 box-shadow-10"
                        *ngIf="isOpenPossessionModal">
                        <div class="d-flex">
                          <div class="w-100 bg-white">
                            <ng-container *ngFor="let type of dateFilterList">
                              <div class="form-check form-check-inline w-fit-content">
                                <input type="radio" id="inpShowData{{type.value}}" name="globalRange"
                                  formControlName="globalRange" [value]="type.value" class="radio-check-input w-10 h-10"
                                  (change)="handlePossessionRangeChange(type.value)">
                                <label class="text-dark-gray text-large ml-8" for="inpShowData{{type.value}}"> {{
                                  type.value === 'Custom Date' ? 'Custom' : type.displayName }}</label>
                              </div>
                            </ng-container>
                            <div class="position-relative dashboard-filter form-group m-6 py-6"
                              [ngClass]="{'pe-none disabled' : propertyDetailForm.controls['globalRange'].value !== 'Custom Date', 'border-red-30': isValidPossDate || propertyDetailForm.controls['possessionDate'].invalid && propertyDetailForm.controls['possessionDate'].touched, 'border': !isValidPossDate && !(propertyDetailForm.controls['possessionDate'].invalid && propertyDetailForm.controls['possessionDate'].touched)}">
                              <form-errors-wrapper [control]="propertyDetailForm.controls['globalDate']" label="Date">
                                <span *ngIf="selectedMonth"
                                  class="fw-700 text-large text-black-200 px-12 w-90pr cursor-pointer"
                                  [owlDateTimeTrigger]="dt5">
                                  {{selectedMonth}} {{selectedYear}}
                                </span>
                                <span *ngIf="!selectedMonth" class="text-dark-gray px-12 w-90pr cursor-pointer"
                                  [owlDateTimeTrigger]="dt5">
                                  select month and year
                                </span>
                                <input type="text" [value]="selectedMonthAndYear" [owlDateTimeTrigger]="dt5"
                                  [owlDateTime]="dt5" placeholder="Select date" class="p-0 border-0 border-remove"
                                  style="height: 0 !important; opacity: 0; position: absolute; top: 0; left: 0; pointer-events: none;" />
                                <owl-date-time #dt5 startView="year" [yearOnly]="true" [pickerType]="'calendar'"
                                  (afterPickerOpen)="onPickerOpened(currentDate, 'month')"
                                  (monthSelected)="monthChanged($event)"></owl-date-time>
                                <div *ngIf="isValidPossDate"
                                  class="mt-8 text-xs text-red position-absolute right-16 fw-semi-bold">
                                  Please select possession date
                                </div>
                              </form-errors-wrapper>
                            </div>
                          </div>
                        </div>
                        <div class="flex-end p-6 border-top">
                          <div class="btn-coal" (click)="closeModal()">Close</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="field-rupees-tag">
                  <div class="field-label">Maintenance Cost</div>
                  <div class="position-relative budget-dropdown">
                    <form-errors-wrapper [control]="propertyDetailForm.controls['maintenanceCost']"
                      label="Maintenance Cost">
                      <input type="number" id="inpPropAmt" data-automate-id="inpPropAmt"
                        placeholder="Enter Maintenance Cost" formControlName="maintenanceCost"
                        (keydown)="onlyNumbers($event)" maxlength="10"
                        [max]="isRental ? null : propertyDetailForm.controls['expectedPrice'].value">
                      <div class="no-validation">
                        <ng-container *ngIf="currency?.length > 1; else showCurrencySymbol">
                          <ng-select formControlName="currency"
                            class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                            <ng-option *ngFor="let curr of currency" [value]="curr.currency">
                              <span [title]="curr.currency">{{curr.currency}}</span>
                            </ng-option>
                          </ng-select>
                        </ng-container>
                        <ng-template #showCurrencySymbol>
                          <h5 class="rupees px-12 py-8 fw-600 m-4"
                            [ngClass]="{'pe-none blinking': isGlobalSettingsLoading || isSelectedPropertyInfoLoading}">
                            {{ selectedPropertyInfo?.monetaryInfo?.currency || defaultCurrency}}</h5>
                        </ng-template>
                      </div>
                    </form-errors-wrapper>
                    <div *ngIf="propertyDetailForm.controls['maintenanceCost'].value"
                      class="position-absolute right-12 bottom-12 text-accent-green fw-semi-bold text-sm">
                      {{maintenanceCostInWords}}</div>
                  </div>
                </div>
                <div class="field-rupees-tag" *ngIf="isRental && !isClickOfficeSpace && !isClickCoWorkingOfficeSpace">
                  <div class="field-label">Deposit/Security Amount</div>
                  <div class="position-relative budget-dropdown">
                    <form-errors-wrapper [control]="propertyDetailForm.controls['depositAmount']"
                      label="Deposit/Security Amount">
                      <input type="number" id="inpPropAmt" data-automate-id="inpPropAmt"
                        placeholder="Enter Deposit/Security Amount" formControlName="depositAmount"
                        (keydown)="onlyNumbers($event)" maxlength="10">
                      <div class="no-validation">
                        <ng-container *ngIf="currency?.length > 1; else showCurrencySymbol">
                          <ng-select formControlName="currency"
                            class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                            <ng-option *ngFor="let curr of currency" [value]="curr.currency">
                              <span [title]="curr.currency">{{curr.currency}}</span>
                            </ng-option>
                          </ng-select>
                        </ng-container>
                      </div>
                    </form-errors-wrapper>
                    <div *ngIf="propertyDetailForm.controls['depositAmount'].value"
                      class="position-absolute right-12 bottom-12 text-accent-green fw-semi-bold text-sm">
                      {{depositAmountInWords}}</div>
                  </div>
                </div>
                <div *ngIf="(isClickOfficeSpace || isClickCoWorkingOfficeSpace) && isRental" class="">
                  <div class="field-label">Security Deposit</div>
                  <div class="w-100 align-center">
                    <div class="flex-grow-1">
                      <form-errors-wrapper [control]="propertyDetailForm.controls['securityDepositAmount']"
                        label="Security Deposit">
                        <input type="number" id="inpSecurityDeposit" data-automate-id="inpSecurityDeposit"
                          placeholder="Enter Security Deposit" formControlName="securityDepositAmount" step="0.01"
                          (input)="updateSecurityDepositUnit($event.target.value)" (keydown)="onlyNumbers($event)">
                      </form-errors-wrapper>
                    </div>
                    <div class="w-120 pl-8">
                      <form-errors-wrapper [control]="propertyDetailForm.controls['securityDepositUnit']" label='Unit'>
                        <ng-select [virtualScroll]="true" tabindex="4" placeholder="Enter Unit"
                          formControlName="securityDepositUnit" [items]="brokerageUnits" bindValue="item"
                          [readonly]="propertyDetailForm.controls['securityDepositAmount'].value ? false : true">
                        </ng-select>
                      </form-errors-wrapper>
                    </div>
                  </div>
                  <div *ngIf="propertyDetailForm.controls['securityDepositAmount'].value"
                    class="position-absolute right-12 bottom-12 text-accent-green fw-semi-bold text-sm">
                    {{securityDepositInWords}}
                  </div>
                </div>
                <div
                  *ngIf="(isClickOfficeSpace || isClickCoWorkingOfficeSpace) && isRental && !globalSettingsDetails?.isCustomLeadFormEnabled"
                  class="field-rupees-tag">
                  <div class="field-label">Common Area Charges</div>
                  <div class="w-100 align-center">
                    <div class="w-70 position-relative budget-dropdown">
                      <form-errors-wrapper>
                        <input type="number" (keydown)="onlyNumbers($event)" [min]="1" id="inpPropSize"
                          data-automate-id="inpPropSize" placeholder="Enter Common Area Charges"
                          formControlName="commonAreaCharges">
                        <div class="no-validation">
                          <ng-container *ngIf="currency?.length > 1 ; else showCurrencySymbol">
                            <ng-select formControlName="currency" (change)="changeCurrency($event)"
                              class="ml-4 mt-4 position-absolute top-0 manage-dropdown" ResizableDropdown>
                              <ng-option *ngFor="let curr of currency" [value]="curr.currency">
                                <span [title]="curr.currency">
                                  {{curr.currency}}
                                </span>
                              </ng-option>
                            </ng-select>
                          </ng-container>
                        </div>
                      </form-errors-wrapper>
                      <div *ngIf="propertyDetailForm.controls['commonAreaCharges'].value"
                        class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                        {{commonAreaChargesInWords}}</div>
                    </div>
                    <div class="w-30 pl-8">
                      <form-errors-wrapper [control]="propertyDetailForm.controls['commonAreaChargesUnit']"
                        label="Size Unit">
                        <ng-select [virtualScroll]="true" tabindex="4" placeholder="Enter Size Unit"
                          [ngClass]="{'pe-none blinking': isAreaSizeUnitsLoading}"
                          formControlName="commonAreaChargesUnit"
                          [required]="propertyDetailForm.controls['commonAreaCharges'].value"
                          [readonly]="!propertyDetailForm.controls['commonAreaCharges'].value" [items]="areaSizeUnits"
                          bindValue="id" bindLabel="unit"></ng-select>
                      </form-errors-wrapper>
                    </div>
                  </div>
                </div>
                <div *ngIf="(isClickOfficeSpace || isClickCoWorkingOfficeSpace) && isRental">
                  <div class="w-100 align-center">
                    <div class="w-50">
                      <div class="field-label">Lock In Period</div>
                      <form-errors-wrapper>
                        <ng-select [virtualScroll]="true" *ngIf="!propertyListIsLoading else fieldLoader"
                          id="lockInPeriod" ResizableDropdown data-automate-id="lockInPeriod" dropdownPosition="bottom"
                          placeholder="Select lock in period" formControlName="lockInPeriod"
                          class="bg-white position-relative align-center">
                          <ng-option *ngFor="let period of lockInPeriodList" [value]="period.value">
                            {{ period.label }}
                          </ng-option>
                        </ng-select>
                      </form-errors-wrapper>
                    </div>
                    <div class="w-50 pl-8">
                      <div class="field-label">Notice Period</div>
                      <form-errors-wrapper>
                        <ng-select [virtualScroll]="true" *ngIf="!propertyListIsLoading else fieldLoader"
                          id="noticePeriod" ResizableDropdown data-automate-id="noticePeriod" dropdownPosition="bottom"
                          placeholder="Select notice period" formControlName="noticePeriod"
                          class="bg-white position-relative align-center">
                          <ng-option *ngFor="let period of noticePeriodList" [value]="period.value">
                            {{ period.label }}
                          </ng-option>
                        </ng-select>
                      </form-errors-wrapper>
                    </div>
                  </div>
                </div>
                <div *ngIf="basicInfoForm.get('enquiredFor').value === 'Rent'" class="field-rupees-tag">
                  <div class="flex-between">
                    <label> <span class="field-label-req mr-10">Rent Amount</span>
                    </label>
                    <div class="align-center mt-12 fw-semi-bold">
                      <label class="checkbox-container">
                        <input type="checkbox" id="inpNegotiable" data-automate-id="inpNegotiable"
                          formControlName="isNegotiable">
                        <span class="checkmark"></span>{{ 'PROPERTY.PROPERTY_DETAIL.negotiable' | translate }}
                      </label>
                    </div>
                  </div>
                  <div class="w-100 d-flex gap-2">
                    <div class="position-relative budget-dropdown w-60pr ph-w-70pr">
                      <form-errors-wrapper [control]="propertyDetailForm.controls['expectedPrice']"
                        [label]="basicInfoForm.get('enquiredFor').value === 'Rent' ? 'Rent Amount' : ('PROPERTY.PROPERTY_DETAIL.total-price' | translate)">
                        <input type="number" id="inpPropAmt" data-automate-id="inpPropAmt"
                          placeholder="Enter Rent Amount" formControlName="expectedPrice"
                          (keydown)="onlyNumbers($event)" maxlength="10">
                        <div class="no-validation">
                          <ng-container *ngIf="currency?.length > 1; else showCurrencySymbol">
                            <ng-select formControlName="currency"
                              class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                              <ng-option *ngFor="let curr of currency" [value]="curr.currency">
                                <span [title]="curr.currency">{{curr.currency}}</span>
                              </ng-option>
                            </ng-select>
                          </ng-container>
                        </div>
                      </form-errors-wrapper>
                      <div *ngIf="propertyDetailForm.controls['expectedPrice'].value"
                        class="position-absolute right-12 bottom-12 text-accent-green fw-semi-bold text-sm">
                        {{budgetInWords}}</div>
                    </div>
                    <div class="w-40pr ph-w-30">
                      <form-errors-wrapper [control]="propertyDetailForm.controls['paymentFrequency']"
                        label='frequency'>
                        <ng-select [virtualScroll]="true" placeholder="Select Frequency"
                          formControlName="paymentFrequency" [items]="paymentList" bindValue="enumValue"
                          bindLabel="displayName" class="border-0"
                          [readonly]="propertyDetailForm.controls['expectedPrice'].value ? false : true">
                        </ng-select>
                      </form-errors-wrapper>
                    </div>
                  </div>
                </div>
                <!-- GST fields removed for Rent/Lease -->
                <div *ngIf="basicInfoForm.get('enquiredFor').value === 'Sale'" class="field-rupees-tag">
                  <div> <span class="field-label-req">{{ 'PROPERTY.PROPERTY_DETAIL.total-price' | translate}}</span>
                    <span class="field-label pl-16" *ngIf="isRental">per month </span>
                    <span class="field-label"
                      [ngClass]="{'pl-16' : !isRental}">{{'PROPERTY.PROPERTY_DETAIL.incl-charges' |
                      translate}}</span>
                  </div>
                  <div class="position-relative budget-dropdown">
                    <form-errors-wrapper [control]="propertyDetailForm.controls['expectedPrice']"
                      label="{{ 'PROPERTY.PROPERTY_DETAIL.total-price' | translate }}">
                      <input type="number" id="inpPropAmt" data-automate-id="inpPropAmt" placeholder="Enter Total Price"
                        formControlName="expectedPrice" (keydown)="onlyNumbers($event)" maxlength="10">
                      <div class="no-validation">
                        <ng-container *ngIf="currency?.length > 1; else showCurrencySymbol">
                          <ng-select formControlName="currency"
                            class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                            <ng-option *ngFor="let curr of currency" [value]="curr.currency">
                              <span [title]="curr.currency">{{curr.currency}}</span>
                            </ng-option>
                          </ng-select>
                        </ng-container>
                      </div>
                    </form-errors-wrapper>
                    <div *ngIf="propertyDetailForm.controls['expectedPrice'].value"
                      class="position-absolute right-12 bottom-12 text-accent-green fw-semi-bold text-sm">
                      {{budgetInWords}}</div>
                  </div>
                </div>
                <div *ngIf="!globalSettingsDetails?.shouldEnablePropertyListing" class="d-flex mt-6">
                  <ng-container *ngFor="let subOption of taxationModeList">
                    <div>
                      <label class="form-check form-check-inline p-0">
                        <input class="radio-check-input" type="radio" formControlName="taxationMode"
                          [value]="subOption.value">
                        <div class="text-large fw-semi-bold cursor-pointer"
                          [ngClass]="propertyDetailForm?.get('taxationMode')?.value === subOption.value ? 'text-black-200': 'text-dark-gray'">
                          {{ subOption.label }}</div>
                      </label>
                    </div>
                  </ng-container>
                </div>
                <div *ngIf="(isClickOfficeSpace || isClickCoWorkingOfficeSpace) && isRental" class="field-rupees-tag">
                  <div class="field-label">Escalation</div>
                  <div class="position-relative budget-dropdown">
                    <form-errors-wrapper label="escalation" [control]="propertyDetailForm.controls['escalation']">
                      <input type="number" (keydown)="onlyNumbers($event)" formControlName="escalation" [min]="1"
                        [max]="99" id="escalation" data-automate-id="escalation" placeholder="Enter Escalation">
                      <h5 class="rupees px-12 py-8 fw-600 m-4">%</h5>
                    </form-errors-wrapper>
                    <!-- <div *ngIf="propertyDetailForm.controls['escalation'].value"
                        class="position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm">
                        {{escalationInWord}}</div> -->
                  </div>
                </div>
                <div class="field-label">{{ 'SIDEBAR.project' | translate }}</div>
                <ng-select [virtualScroll]="true" [items]="projectList" [addTag]="true" formControlName="project"
                  addTagText="Create New Project" class="bg-white"
                  [ngClass]="{'pe-none blinking': isProjectListLoading}"
                  placeholder="{{ 'GLOBAL.select' | translate }}/Create {{ 'SIDEBAR.project' | translate }}">
                </ng-select>
                <div>
                  <div
                    [ngClass]="(propertyDetailForm.controls['brokerage'].value > 0 && propertyDetailForm.controls['brokerageUnit'].value) ? 'field-label-req' : 'field-label'">
                    {{'PROPERTY.PROPERTY_DETAIL.brokerage' | translate}}</div>
                  <div class="w-100 align-center">
                    <div class="flex-grow-1">
                      <form-errors-wrapper [control]="propertyDetailForm.controls['brokerage']"
                        label="{{ 'PROPERTY.PROPERTY_DETAIL.brokerage' | translate }}">
                        <input type="number" id="inpPropBrokerage" data-automate-id="inpPropBrokerage"
                          placeholder="Enter Brokerage" formControlName="brokerage" min="0.01" step="0.01"
                          (input)="updateBrokerageUnit($event.target.value)"
                          (keydown)="onlyNumbersWithDecimal($event, $event.target.value)">
                      </form-errors-wrapper>
                    </div>
                    <div class="w-120 pl-8">
                      <form-errors-wrapper [control]="propertyDetailForm.controls['brokerageUnit']" label='Unit'>
                        <ng-select [virtualScroll]="true" tabindex="4" placeholder="Select Unit"
                          formControlName="brokerageUnit" [items]="brokerageUnits" bindValue="item"
                          [readonly]="propertyDetailForm.controls['brokerage'].value ? false : true">
                        </ng-select>
                      </form-errors-wrapper>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <div class="flex-between">
                    <div class="field-label">Notes</div>
                    <div *ngIf="propertyDetailForm.controls['notes'].value" class="cursor-pointer mt-16"
                      (click)="openPreview('',true)">
                      <h6 class="flex-center text-green-250 fw-400"><span
                          class="ic-eye-solid ic-xxs ic-accent-green mr-4"></span>Preview
                      </h6>
                    </div>
                  </div>
                  <form-errors-wrapper>
                    <textarea rows="3" placeholder="Enter Notes" formControlName="notes" class="scrollbar"></textarea>
                  </form-errors-wrapper>
                </div>
                <ng-container *ngIf="isShowManualLocation == false else manualLocation">
                  <div class="field-label-req">{{'LOCATION.location' |
                    translate}}</div>
                  <form-errors-wrapper [control]="propertyDetailForm.controls['locationId']"
                    label="{{ 'LOCATION.location' | translate }}">
                    <div class="field-tag">
                      <ng-select [virtualScroll]="true" formControlName="locationId"
                        (search)="searchPlaceTerm$.next($event.term)" [editableSearchTerm]="true"
                        placeholder="Select Location" class="bg-white" [items]="placesList" bindLabel="location"
                        (clear)="removeLocation('location')" (change)="removeLocation('changeLocation')">
                        <ng-template ng-option-tmp let-item="item">
                          <div title="{{item?.location}}">{{item?.location}}</div>
                        </ng-template>
                      </ng-select>
                      <div class="position-absolute top-5 right-40" *ngIf="isPlacesListLoading">
                        <img src="assets/images/loader-rat.svg" class="rat-loader h-30px w-30px" alt="loader">
                      </div>
                      <div class="search icon ic-search ic-sm ic-coal"></div>
                    </div>
                  </form-errors-wrapper>
                </ng-container>
                <ng-template #manualLocation>
                  <div class="field-label-req">Locality</div>
                  <form-errors-wrapper [control]="propertyDetailForm.controls['enquiredLocality']" label="Locality">
                    <input type="text" formControlName="enquiredLocality" placeholder="Enter Locality"
                      (change)="removeLocation('changeLocality')">
                  </form-errors-wrapper>
                  <div class="align-center ph-flex-col">
                    <div class="form-group w-50 mr-16 ph-w-100 ph-mr-0">
                      <div class="field-label">City</div>
                      <form-errors-wrapper [control]="propertyDetailForm.controls['enquiredCity']" label="City">
                        <input type="text" formControlName="enquiredCity" placeholder="Enter City"
                          (change)="removeLocation('changeLocality')">
                      </form-errors-wrapper>
                    </div>
                    <div class="form-group w-50 ph-w-100">
                      <div class="field-label">State</div>
                      <form-errors-wrapper [control]="propertyDetailForm.controls['enquiredState']" label="State">
                        <input type="text" formControlName="enquiredState" placeholder="Enter State"
                          (change)="removeLocation('changeLocality')">
                      </form-errors-wrapper>
                    </div>
                  </div>
                  <div class="align-center ph-flex-col">
                    <div class="form-group w-50 mr-16 ph-w-100 ph-mr-0">
                      <div class="field-label">Country</div>
                      <form-errors-wrapper [control]="propertyDetailForm.controls['enquiredCountry']" label="Country">
                        <input type="text" formControlName="enquiredCountry" placeholder="Enter Country"
                          (change)="removeLocation('changeLocality')">
                      </form-errors-wrapper>
                    </div>
                    <div class="form-group w-50 ph-w-100">
                      <div class="field-label">Pin Code</div>
                      <form-errors-wrapper>
                        <input type="text" formControlName="enquiredPincode" placeholder="Enter Pin Code"
                          (change)="removeLocation('changeLocality')">
                      </form-errors-wrapper>
                    </div>
                  </div>
                  <!-- <div class="align-center ph-flex-col">
                    <div class="form-group w-50 mr-16 ph-w-100 ph-mr-0">
                      <div class="field-label">Country</label>
                      <input type="text" formControlName="country" placeholder="ex. India"
                        (change)="removeLocation('changeLocality')">
                    </div>
                    <div class="form-group w-50 ph-w-100">
                      <div class="field-label">Pincode</label>
                      <input type="text" formControlName="postalCode" placeholder="ex. 560010"
                        (change)="removeLocation('changeLocality')">
                    </div>
                  </div> -->
                </ng-template>
                <div class="d-flex">
                  <div class="cursor-pointer align-center fw-semi-bold text-accent-green mt-10"
                    (click)="addManualLocation()">
                    <span class="icon ic-xs ic-accent-green"
                      [ngClass]="isShowManualLocation ? 'ic-search' : 'ic-add'"></span>
                    <span>{{isShowManualLocation ? 'Location List' : 'Manually Enter Location'}}</span>
                  </div>
                </div>
              </div>
              <div class="border-right mx-20 mt-20 mb-40 flex-grow-1 ip-d-none"></div>
              <div class="w-50 ip-w-100">
                <div *ngIf="(isClickOfficeSpace || isClickCoWorkingOfficeSpace) && isRental">
                  <label class="field-label">Tenant POC Name</label>
                  <form-errors-wrapper>
                    <input type="text" id="tenantPOCName" data-automate-id="tenantPOCName"
                      formControlName="tenantPOCName" placeholder="Enter Tenant POC Name">
                  </form-errors-wrapper>
                </div>
                <div *ngIf="isClickOfficeSpace && isRental">
                  <label class="field-label">Tenant POC Designation</label>
                  <form-errors-wrapper>
                    <input type="text" id="tenantPOCdesignation" data-automate-id="tenantPOCdesignation"
                      formControlName="tenantPOCdesignation" placeholder="Enter Tenant POC Designation">
                  </form-errors-wrapper>
                </div>
                <div *ngIf="(isClickOfficeSpace || isClickCoWorkingOfficeSpace) && isRental" class="position-relative">
                  <label class="field-label">Tenant POC Phone</label>
                  <form-errors-wrapper>
                    <ngx-mat-intl-tel-input #tenantPOCPInput *ngIf="hasInternationalSupport"
                      [preferredCountries]="preferredCountries" [enablePlaceholder]="true" [enableSearch]="true"
                      class="phoneNumber no-validation" formControlName="tenantPOCPhone">
                    </ngx-mat-intl-tel-input>
                    <ngx-mat-intl-tel-input #tenantPOCPInput *ngIf="!hasInternationalSupport"
                      [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                      [enablePlaceholder]="true" [enableSearch]="true" class="phoneNumber no-validation"
                      formControlName="tenantPOCPhone">
                    </ngx-mat-intl-tel-input>
                  </form-errors-wrapper>
                  <small class="text-red  flex-end right-10 text-xxs mr-8 position-absolute"
                    *ngIf="propertyDetailForm.get('tenantPOCPhone')?.errors?.validatePhoneNumber">
                    Please enter a valid phone number.
                  </small>
                </div>
                <div *ngIf="isClickCoWorkingOfficeSpace && isRental">
                  <label class="field-label">Co Working Operator POC Name</label>
                  <form-errors-wrapper>
                    <input type="text" id="coWorkingOperatorPOCName" data-automate-id="coWorkingOperatorPOCName"
                      formControlName="coWorkingOperatorPOCName" placeholder="Enter Co Working Operator POC Name">
                  </form-errors-wrapper>
                </div>
                <div *ngIf="isClickCoWorkingOfficeSpace && isRental" class="position-relative">
                  <label class="field-label">Co Working Operator POC Phone</label>
                  <form-errors-wrapper>
                    <ngx-mat-intl-tel-input #coWorkingOperatorPOCInput *ngIf="hasInternationalSupport"
                      [preferredCountries]="preferredCountries" [enablePlaceholder]="true" [enableSearch]="true"
                      class="phoneNumber no-validation" formControlName="coWorkingOperatorPOCInput">
                    </ngx-mat-intl-tel-input>
                    <ngx-mat-intl-tel-input #coWorkingOperatorPOCInput *ngIf="!hasInternationalSupport"
                      [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                      [enablePlaceholder]="true" [enableSearch]="true" class="phoneNumber no-validation"
                      formControlName="coWorkingOperatorPOCInput">
                    </ngx-mat-intl-tel-input>
                  </form-errors-wrapper>
                  <small class="text-red  flex-end right-10 text-xxs mr-8 position-absolute"
                    *ngIf="propertyDetailForm.get('coWorkingOperatorPOCInput')?.errors?.validatePhoneNumber">
                    Please enter a valid phone number.
                  </small>
                </div>
                <ng-container *ngIf="canViewOwner">
                  <!-- Multiple Property Contacts -->
                  <div class="mt-20">
                    <div class="flex-between mb-10">
                      <div class="field-label">Contact Information</div>
                      <div class="cursor-pointer align-center fw-semi-bold text-accent-green" (click)="addContact()">
                        <span class="icon ic-xs ic-accent-green ic-add"></span>
                        <span class="text-accent-green">{{newContactAdded ? 'Adding Contact' : 'Add Contact'}}</span>
                      </div>
                    </div>
                    <div formArrayName="propertyContacts">
                      <div *ngFor="let contact of propertyContacts.controls; let i = index" [formGroupName]="i"
                        class="p-16 rounded border mt-10">
                        <div class="flex-between" *ngIf="i > 0">
                          <div class="field-label mt-0">Owner/Builder Name {{i+1}}</div>
                          <div class="cursor-pointer align-center fw-semi-bold text-red" (click)="removeContact(i)">
                            <span class="icon ic-xxxs ic-red ic-close mr-2"></span>
                            <span class="text-xs">Remove</span>
                          </div>
                        </div>
                        <div *ngIf="i === 0" class="field-label mt-0">Owner/Builder Name</div>
                        <div class="form-group">
                          <form-errors-wrapper [control]="contact.get('name')"
                            [label]="i === 0 ? 'Owner Name' : 'Owner Name ' + (i+1)">
                            <input type="text" placeholder="Enter Owner/Builder Name" formControlName="name">
                          </form-errors-wrapper>
                        </div>
                        <div class="d-flex flex-col gap-2">
                          <div class="w-100">
                            <div class="field-label">Phone Number</div>
                            <form-errors-wrapper [control]="contact.get('phone')" label="Phone Number">
                              <ngx-mat-intl-tel-input *ngIf="hasInternationalSupport"
                                [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                                [enableSearch]="true" formControlName="phone" class="no-validation">
                              </ngx-mat-intl-tel-input>
                              <ngx-mat-intl-tel-input *ngIf="!hasInternationalSupport"
                                [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                                [enablePlaceholder]="true" [enableSearch]="true" formControlName="phone"
                                class="no-validation">
                              </ngx-mat-intl-tel-input>
                            </form-errors-wrapper>
                          </div>
                          <div class="w-100">
                            <div class="field-label">Alternate Number</div>
                            <form-errors-wrapper [control]="contact.get('alternatePhone')" label="Alternate Number">
                              <ngx-mat-intl-tel-input *ngIf="hasInternationalSupport"
                                [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                                [enableSearch]="true" formControlName="alternatePhone" class="no-validation">
                              </ngx-mat-intl-tel-input>
                              <ngx-mat-intl-tel-input *ngIf="!hasInternationalSupport"
                                [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                                [enablePlaceholder]="true" [enableSearch]="true" formControlName="alternatePhone"
                                class="no-validation">
                              </ngx-mat-intl-tel-input>
                            </form-errors-wrapper>
                          </div>
                        </div>
                        <div class="form-group">
                          <div class="field-label">Email</div>
                          <form-errors-wrapper [control]="contact.get('email')" label="Email">
                            <input type="email" placeholder="Enter Owner/Builder Email" formControlName="email">
                          </form-errors-wrapper>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
                <form [formGroup]="attributeForm" novalidate>
                  <!-- Render all other attributes first -->
                  <ng-container *ngFor="let attr of basicAttributes">
                    <ng-container *ngIf="!isFloorAttribute(attr.attributeName)">
                      <div class="box-radio">
                        <div class="field-label">{{ attr.attributeDisplayName }}</div>
                        <form-errors-wrapper [control]="attributeForm.controls[attr.attributeName]"
                          [label]="attr.attributeDisplayName">
                          <div class="d-flex flex-wrap">
                            <ng-container
                              *ngFor="let attrNo of attributeExpandedLists[attr.attributeName] || [1, 2, 3, 4, '5+']">
                              <input type="radio" class="btn-check" autocomplete="off"
                                [formControlName]="attr.attributeName"
                                (click)="appendToAttrList(attrNo, attr.attributeName)"
                                [id]="'inpAttr' + attr.attributeName + '_' + attrNo" [value]="attrNo" />
                              <label class="btn-outline w-18pr" [for]="'inpAttr' + attr.attributeName + '_' + attrNo">
                                {{ attrNo }}
                              </label>
                            </ng-container>
                          </div>
                        </form-errors-wrapper>
                      </div>
                    </ng-container>
                  </ng-container>
                  <!-- Append dropdowns for numberOfFloors and floorNumber -->
                  <!-- Total Floors dropdown -->
                  <ng-container *ngIf="!isAgricultural">
                    <div class="box-radio form-group w-100" *ngIf="attributeForm.controls['numberOfFloors']">
                      <div class="field-label">Total Floors</div>
                      <form-errors-wrapper [control]="attributeForm.controls['numberOfFloors']" label="Total Floors">
                        <ng-select [formControlName]="'numberOfFloors'" id="selAttrTotalFloors">
                          <ng-option *ngFor="let val of getFloorOptions('numberOfFloors')" [value]="val">
                            {{ val }}
                          </ng-option>
                        </ng-select>
                      </form-errors-wrapper>
                    </div>
                    <!-- Floor Number dropdown -->
                    <div class="box-radio form-group w-100" *ngIf="attributeForm.controls['floorNumber']">
                      <div class="field-label">Floor Number</div>
                      <form-errors-wrapper [control]="attributeForm.controls['floorNumber']" label="Floor Number">
                        <ng-select [formControlName]="'floorNumber'" id="selAttrFloorNumber">
                          <ng-option *ngFor="let val of getFloorOptions('floorNumber')" [value]="val">
                            {{ val }}
                          </ng-option>
                        </ng-select>
                      </form-errors-wrapper>
                    </div>
                  </ng-container>
                  <div *ngIf="(isClickOfficeSpace || isClickCoWorkingOfficeSpace) && isRental" class="">
                    <div class="field-label">Number Of Floor Occupied</div>
                    <form-errors-wrapper>
                      <ng-select [items]="selectedTotalFloor" bindLabel="fullName" ResizableDropdown bindValue="id"
                        [multiple]="true" groupBy="noOfFloorsOccupied" [selectableGroup]="true" [closeOnSelect]="false"
                        [clearSearchOnAdd]="true" name="noOfFloorsOccupied" placeholder="2nd floor and 4th floor"
                        class="bg-white" formControlName="noOfFloorsOccupied" [readonly]="!selectedTotalFloor.length">
                        <ng-template ng-header-tmp let-item="item" let-item$="item$" let-index="index">
                          <div class="flex-between position-relative">
                            <u class="position-relative nleft-8">Total Floors</u>
                            <span class="text-accent-green cursor-pointer"
                              (click)="handleSelectAll()">{{isAllSelected?'Unselect All':'Select All'}}</span>
                          </div>
                        </ng-template>
                        <ng-template ng-label-tmp let-item="item">
                          {{(item) ? ('Floor ' + item) : "All"}}
                        </ng-template>
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                          <div class="checkbox-container" (click)="lastClickedOption = item">
                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                              [checked]="item$.selected">
                            <span class="checkmark"></span>Floor {{item}}
                          </div>
                        </ng-template>
                      </ng-select>
                    </form-errors-wrapper>
                  </div>
                  <div class="box-radio position-relative">
                    <div class="field-label">{{'PROPERTY.PROPERTY_DETAIL.furnish-status' | translate }}</div>
                    <div class="d-flex flex-wrap">
                      <ng-container *ngFor="let furnishStatus of furnishStatusList">
                        <input type="radio" class="btn-check" name="furnishStatus" [id]="furnishStatus.dispName"
                          autocomplete="off" [value]="furnishStatus.dispName" formControlName="furnishStatus">
                        <label class="btn-outline" [for]="furnishStatus.dispName">{{furnishStatus.dispName}}</label>
                      </ng-container>
                    </div>
                  </div>
                  <div class="box-radio position-relative">
                    <div class="field-label">{{'PROPERTY.PROPERTY_DETAIL.facing' | translate }}</div>
                    <div class="d-flex flex-wrap">
                      <ng-container *ngFor="let facing of facingList">
                        <input type="radio" class="btn-check" name="facing" [id]="facing.displayName" autocomplete="off"
                          [value]="facing.displayName" formControlName="facing">
                        <label class="btn-outline" [for]="facing.displayName">{{facing.displayName}}</label>
                      </ng-container>
                    </div>
                  </div>
                </form>
                <rating (onRatingInput)="patchFormControlValue(propertyDetailForm, 'rating' , $event)"
                  [userInput]="propertyDetailForm.value.rating"></rating>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div *ngIf="currentStep==3">
        <div class="ip-h-60 w-100 bg-white">
          <div
            class="position-fixed right-20 top-55 align-center z-index-2 ip-top-100 ip-w-100 ip-bg-white ip-p-8 ip-right-0">
            <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="goToManage()">{{
              'BUTTONS.cancel' | translate }}</u>
            <button class="btn-gray btn-xs" (click)="handlePreviousClick()">{{ 'BUTTONS.back' | translate }}</button>
            <button class="btn-coal btn-xs ml-20" (click)="handleNextClick()">{{ 'BUTTONS.next' | translate
              }}</button>
          </div>
        </div>
        <div class="px-20 pt-12 flex-grow-1" [ngClass]="{'pe-none blinking': isSelectedPropertyInfoLoading}">
          <custom-amenities-attributes [propertyTypeInput]="isPlot ? 2 : propType"
            [userSelectedAttributes]="(selectedAdditionalAttr?.length ? selectedAdditionalAttr : selectedAdditionalAttr) || []"
            [userSelectedAmenities]="(selectedAmenities?.length ? selectedAmenities : selectedPropertyInfo?.amenities) || []"
            [userSelectedAmenities]="(selectedAmenities?.length ? selectedAmenities : selectedPropertyInfo?.amenities) || []"
            (selectedAmenities)="addSelectedAmenities($event)"
            (selectedAdditionalAttr)="onAdditionalAttrSelection($event)">
          </custom-amenities-attributes>
          <!-- <amenities-list [propertyTypeInput]="isPlot ? 2 : propType"
            [userSelectedAmenities]="(selectedAmenities?.length ? selectedAmenities : selectedPropertyInfo?.amenities) || []"
            (selectedAmenities)="addSelectedAmenities($event)"></amenities-list> -->
        </div>
      </div>
      <div [ngClass]="{'pe-none blinking': isPropertyAddIsLoading}" *ngIf="currentStep==4">
        <div class="ip-h-60 w-100 bg-white">
          <div
            class="position-fixed right-20 top-55 align-center z-index-2 ip-top-100 ip-w-100 ip-bg-white ip-p-8 ip-right-0">
            <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="goToManage()">{{
              'BUTTONS.cancel' | translate }}</u>
            <button class="btn-gray btn-xs" (click)="handlePreviousClick()">{{ 'BUTTONS.back' | translate }}</button>
            <button class="btn-coal btn-xs ml-20" (click)="updateSecondStep()"
              [routerLink]="'/properties/manage-properties'">
              {{ 'BUTTONS.save' | translate }}
            </button>
          </div>
        </div>
        <div [ngClass]="{'pe-none blinking': isPropertyUpdateIsLoading}"
          class="p-20 m-20 scrollbar h-100-130 scroll-hide bg-white br-6">
          <span class="fw-600 header-5 pb-1 border-bottom-slate-40">Property gallery</span>
          <div class="flex-between input-sm position-relative">
            <div class="align-center w-100 mt-16">
              <h5 class="fw-600 text-accent-green">Third Party URL</h5>
              <div class="flex-grow-1 border-bottom mx-16"></div>
            </div>
            <input type="text" placeholder="Third Party URL" [(ngModel)]="newUrl"
              class="outline-0 w-180 br-20 padd-r mt-8 pr-36" (keyup.enter)="focusableURL.click()">
            <div class="bg-black-600 position-absolute top-14 right-6 dot dot-md cursor-pointer" #focusableURL
              (click)="addInputField()"><span class="icon  ic-plus ic-x-xs"></span>
            </div>
          </div>
          <h6 *ngIf="links?.length" class="header-6 text-dark-gray">Only .URL will save</h6>
          <div class="d-flex flex-wrap mt-12">
            <div class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm" *ngFor="let link of links">
              <a class="fw-600  text-truncate-1 break-all  text-sm mr-4 text-black-100 cursor-pointer border-bottom-slate-40"
                href="{{link}}" target="_blank">{{link}}</a>
              <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                (click)="removeInputField(link)"></span>
            </div>
          </div>
          <div class="align-center w-100 mt-16">
            <h5 class="fw-600 text-accent-green">Photos</h5>
            <div class="flex-grow-1 border-bottom ml-12"></div>
            <label *ngIf="waterMarkSettingsObj.isWaterMarkEnabled"
              class="checkbox-container w-fit-content text-secondary ml-20 mt-0">
              Add water mark
              <input type="checkbox" class="mr-10" [(ngModel)]="waterMarkSettingsObj.toAddWaterMark"
                (change)="waterMarkSettingsObj.toAddWaterMark = $event.target?.checked">
              <span class="checkmark"></span>
            </label>
          </div>
          <!-- <div class="text-mud text-sm">{{'PROPERTY.upload-images'|translate}} ....</div> -->
          <div *ngIf="!isGalleryCarouselVisible" class="mt-8 text-gray fw-semi-bold align-center">
            {{'PROPERTY.GALLERY.file-type' | translate}}</div>
          <ng-container *ngIf="isGalleryCarouselVisible; else gallery">
            <gallery-carousel [imageGallery]="galleryS3Paths" [index]="imageIndex"
              (closeCarousel)="isGalleryCarouselVisible = $event"></gallery-carousel>
          </ng-container>
          <ng-template #gallery>
            <div class="d-flex flex-wrap mt-16">
              <div class="mr-20 mb-16">
                <label>
                  <div class="add-image h-120 w-120">
                    <div class="upload-button flex-center-col">
                      <ng-lottie [options]='addImage' width="70px"></ng-lottie>
                      <div class="fw-600 text-xs">+ Add Photos</div>
                      <div class="text-gray text-xxs fw-semi-bold">(max upload limit 500 MB)</div>
                      <file-upload #fileUploadComponent requiredFileType="image/x-png,image/gif,image/jpeg,image/tiff"
                        [prevUploadedImages]="galleryS3Paths" (imageFiles)="uploadImage($event)"
                        (uploadedFileSize)="selectedFileSize = $event" [isImageGallery]="true" [multiple]="true"
                        (addWaterMarkImage)="waterMarkImages($event)" (imageDimensions)="checkImageDimensions($event)">
                      </file-upload>
                    </div>
                  </div>
                </label>
              </div>
              <div *ngFor="let url of galleryS3Paths; let i = index" class="position-relative mr-20 mb-16"
                draggable="true" (dragstart)="onDragStart(i, $event)" (dragover)="onDragOver(i, $event)"
                (dragleave)="onDragLeave()" (drop)="onDrop($event)">
                <img (click)="openImage(i)" [appImage]="getAWSImagePath(url)" [type]="'property'" width="120"
                  height="120" class="obj-cover cursor-pointer br-5" alt="img" />
                <div class="position-absolute top-4 right-4">
                  <!-- Cover pic selection boolean check needs to be added here and push it galleryImageObject with key as coverImage -->
                  <span (click)="deleteImage(i)" class="dot bg-black-6 cursor-pointer bg-hover-red">
                    <span class="icon ic-delete ic-x-xs" id="clkDeleteImage" data-automate-id="clkDeleteImage"></span>
                  </span>
                </div>
                <div [ngClass]="{'gallery-cover-selected': i === coverImgIndex}" class="gallery-cover"
                  (click)="onSetCoverImage(url, i)">
                  <span class="icon ic-star-full ic-xxxs mr-4"></span>Cover
                </div>
                <div class="position-absolute w-100 bottom-0 image-dropdown">
                  <ng-select [virtualScroll]="true" [addTag]="true" placeholder="Select field"
                    [ngClass]="{'pe-none blinking': isGalleryDropdownDataLoading}"
                    (change)="setImageCategory($event, url)" [(ngModel)]="galleryMapping[url]"
                    [items]="galleryDropdownData"></ng-select>
                </div>
              </div>
              <ng-container *ngIf="isImageUploading">
                <ng-container *ngFor="let item of galleryImageArray" [ngTemplateOutlet]="dotLoader"></ng-container>
              </ng-container>
            </div>
          </ng-template>
          <div class="align-center w-100 mt-16">
            <h5 class="fw-600 text-accent-green">Videos</h5>
            <div class="flex-grow-1 border-bottom ml-12"></div>
          </div>
          <div class="mt-8 text-gray fw-semi-bold">Only .mp4 file will be accepted.</div>
          <div class="mt-16 d-flex flex-wrap">
            <div class="mr-20 mb-16">
              <label>
                <div class="add-image h-120 w-120">
                  <div class="upload-button flex-center-col">
                    <ng-lottie [options]='addVideo' width="70px"></ng-lottie>
                    <div class="fw-600 text-xs">+ Add Videos</div>
                    <div class="text-gray text-xxs fw-semi-bold">(max upload limit 500 MB)</div>
                    <file-upload
                      requiredFileType="video/mp4,video/mpeg,video/quicktime,video/webm,video/x-msvideo,video/x-ms-wmv"
                      (imageFileName)="uploadVideo($event)" [isImageGallery]="true">
                    </file-upload>
                  </div>
                </div>
              </label>
            </div>
            <div *ngFor="let url of galleryS3PathsVid; let i = index" class="position-relative mr-20 mb-16 br-5">
              <video width="120" height="120" controls class="br-5" alt="video">
                <source [src]="url.imageFilePath" type="video/mp4">
                Your browser does not support the video tag.
              </video>
              <div class="position-absolute top-4 right-4">
                <span (click)="deleteVideo(i)" class="dot bg-black-6 cursor-pointer bg-hover-red">
                  <span class="icon ic-delete ic-x-xs" id="clkDeleteImage" data-automate-id="clkDeleteImage"></span>
                </span>
              </div>
              <div class="position-absolute w-100 bottom-0 image-dropdown">
                <div class="p-1 flex-between bg-white-100 brbr-6 brbl-6">
                  <div class="text-truncate-1 break-all fw-semi-bold text-sm">{{url.name ? url.name : 'Video '}}</div>
                </div>
              </div>
            </div>
            <ng-container *ngIf="isVideoUploading">
              <ng-container *ngFor="let item of galleryVideoArray" [ngTemplateOutlet]="dotLoader"></ng-container>
            </ng-container>
          </div>
          <div class="align-center w-100 mt-16">
            <h5 class="fw-600 text-accent-green">Brochures</h5>
            <div class="flex-grow-1 border-bottom ml-12"></div>
          </div>
          <div class="mt-8 mb-12 text-gray fw-semi-bold">Only .pdf file will be accepted.</div>
          <ng-container>
            <div class="d-flex flex-wrap">
              <div class="mr-20 mb-16">
                <label>
                  <div class="add-image h-120 w-120">
                    <div class="upload-button flex-center-col">
                      <ng-lottie [options]='addDoc' width="70px"></ng-lottie>
                      <div class="fw-600 text-xs mt-1">+ Add Documents</div>
                      <div class="text-gray text-xxs fw-semi-bold">(max upload limit 15 MB)</div>
                      <file-upload requiredFileType="application/pdf"
                        (uploadedFile)="selectedFile = $event; fileUploadToS3()"
                        (uploadedFileSize)="selectedFileSize = $event" (uploadedFileName)="fileName = $event">
                      </file-upload>
                    </div>
                  </div>
                </label>
              </div>
              <div *ngFor="let brochure of docList; let i = index" class="mr-30 mb-20 w-130 h-120 flex-col">
                <a [href]="s3BucketUrl+brochure?.url" target="_blank">
                  <img src="../../../../assets/images/pdf.svg" alt=""></a>
                <div class="p-10 flex-between bg-secondary brbr-6 brbl-6">
                  <div class="text-truncate-1 break-all fw-semi-bold">{{brochure.name ? brochure.name : 'property file '
                    +
                    (i+1)}}</div>
                  <span (click)="initDeleteDocument(i)" class="dot bg-light-red cursor-pointer">
                    <div title="Delete" class="ic-delete icon ic-xxxs cursor-pointer" id="clkDeletePdf"
                      data-automate-id="clkDeletePdf"></div>
                  </span>
                </div>
                <br />
              </div>
              <ng-container *ngIf="isDocumentUploading">
                <ng-container [ngTemplateOutlet]="dotLoader"></ng-container>
              </ng-container>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
    <property-preview [imgPath]="coverImg" [ngClass]="{'pe-none blinking': isSelectedPropertyInfoLoading}"
      [liveBasicInfoFormValues]="basicInfoFormValues" [livePropInfoFormValues]="propInfoFormValues"
      [liveAttrFormValues]="attrFormValues" [userSelectedAmenities]="selectedAmenities"
      [propInfo]="selectedPropertyInfo"></property-preview>
  </div>
</ng-container>
<ng-template #deleteDocumentModal>
  <remove-document (onSaveChanges)="removeDocument(currentDelete)" (onHide)="modalRef.hide()"></remove-document>
</ng-template>
<ng-template #previewModal>
  <div class="w-100 br-10">
    <div class="p-20">
      <div class="flex-between">
        <h5 class="fw-600">
          {{isForNotes ? 'Notes' : 'About Property'}} <span *ngIf="previewLanguage === 'eng'">(eng)</span>
          <span *ngIf="previewLanguage === 'arabic'">(arabic)</span>
        </h5>
        <div class="cursor-pointer flex-center header-5 fw-400 text-red-350" (click)="modalRef.hide()">
          <span class="ic-close ic-xxs mb-2 ic-red-350 mr-4"></span>Close
        </div>
      </div>
      <div class="scrollbar max-h-400 border br-6 mt-8 p-16">
        <h6 *ngIf="isForNotes" [innerHTML]="convertUrlsToLinks(getPreviewContent(),true)"
          class="text-sm text-black-20 pre-whitespace"></h6>
        <h6 *ngIf="!isForNotes" class="text-sm text-black-20 pre-whitespace">{{ getPreviewContent() }}</h6>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #dotLoader>
  <div class="w-120 h-120 overlay mr-20 mb-16 br-4 border align-center">

    <div class="container px-4 d-inline flex-center">
      <span>Uploading&nbsp;</span><ng-container *ngFor="let dot of [1, 2, 3]">
        <div class="dot-falling"></div>
      </ng-container>
    </div>
  </div>
</ng-template>
<ng-template #resolutionWarningModal>
  <div class="w-100 br-10">
    <div class="p-20">
      <div class="flex-between">
        <h5 class="fw-600">Low Resolution Images</h5>
        <div class="cursor-pointer flex-center header-5 fw-400 text-red-350" (click)="modalRef.hide()">
          <span class="ic-close ic-xxs mb-2 ic-red-350 mr-4"></span>Close
        </div>
      </div>
      <div class="mt-12">
        <p class="text-sm text-black-20">
          The following images don't meet the minimum resolution requirement of {{MIN_IMAGE_WIDTH}}x{{MIN_IMAGE_HEIGHT}}
          pixels:
        </p>
        <div class="mt-8 scrollbar max-h-200 border br-6 p-16">
          <ul class="pl-16">
            <li *ngFor="let img of lowResolutionImages; let i = index" class="mb-4">
              {{img.fileName || 'Image ' + (i+1)}} ({{img.width}}x{{img.height}} pixels)
            </li>
          </ul>
        </div>
        <p class="mt-12 text-sm text-black-20">
          Low-resolution images may appear blurry or pixelated. Would you like to continue with these images or upload
          higher resolution ones?
        </p>
        <div class="d-flex justify-content-end mt-16">
          <button class="btn-gray btn-xs px-8 mr-12" (click)="continueWithLowResImages()">Continue Anyway</button>
          <button class="btn-coal btn-xs" (click)="cancelLowResImages()">Upload New Images</button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #buttonDots>
  <div class="container px-4">
    <ng-container *ngFor="let dot of [1,2,3]">
      <div class="dot-falling dot-white"></div>
    </ng-container>
  </div>
</ng-template>